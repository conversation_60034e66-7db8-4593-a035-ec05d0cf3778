/* 液态玻璃效果 CSS 变量定义 */
:root {
  /* 液态玻璃基础变量 */
  --lg-glass-bg: rgba(255, 255, 255, 0.1);
  --lg-glass-bg-hover: rgba(255, 255, 255, 0.15);
  --lg-glass-bg-active: rgba(255, 255, 255, 0.2);
  --lg-glass-border: rgba(255, 255, 255, 0.2);
  --lg-glass-shadow: 0 8px 32px rgba(31, 38, 135, 0.37);
  --lg-glass-shadow-hover: 0 12px 40px rgba(31, 38, 135, 0.5);
  
  /* 边框圆角 */
  --lg-radius-sm: 8px;
  --lg-radius-md: 12px;
  --lg-radius-lg: 16px;
  --lg-radius-xl: 20px;
  --lg-radius-2xl: 24px;
  --lg-radius-full: 9999px;
  
  /* 动画缓动函数 */
  --lg-ease-smooth: cubic-bezier(0.4, 0, 0.2, 1);
  --lg-ease-bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);
  
  /* 文字颜色 */
  --lg-text-primary: #ffffff;
  --lg-text-secondary: rgba(255, 255, 255, 0.8);
  --lg-text-placeholder: rgba(255, 255, 255, 0.6);
  --lg-text-dark: #0f0303;
  
  /* Element Plus 组件自定义变量 */
  /* Button 组件 */
  --el-button-bg-color: var(--lg-glass-bg);
  --el-button-border-color: var(--lg-glass-border);
  --el-button-text-color: var(--lg-text-primary);
  --el-button-hover-bg-color: var(--lg-glass-bg-hover);
  --el-button-hover-border-color: var(--lg-glass-border);
  --el-button-active-bg-color: var(--lg-glass-bg-active);
  --el-button-active-border-color: var(--lg-glass-border);
  
  /* Input 组件 */
  --el-input-bg-color: var(--lg-glass-bg);
  --el-input-border-color: var(--lg-glass-border);
  --el-input-text-color: var(--lg-text-dark);
  --el-input-placeholder-color: var(--lg-text-dark);
  --el-input-hover-border-color: var(--lg-glass-border);
  --el-input-focus-border-color: #3b82f6;
  --el-input-border-radius: var(--lg-radius-2xl);
  
  /* Table 组件 */
  --el-table-bg-color: transparent;
  --el-table-text-color: var(--lg-text-primary);
  --el-table-header-bg-color: #ffffff;
  --el-table-header-text-color: #333333;
  --el-table-tr-bg-color: rgba(102, 126, 234, 0.3);
  --el-table-row-hover-bg-color: rgba(102, 126, 234, 0.5);
  --el-table-border-color: var(--lg-glass-border);
  
  /* Dialog 组件 */
  --el-dialog-bg-color: var(--lg-glass-bg);
  --el-dialog-border-radius: var(--lg-radius-2xl);
  --el-dialog-box-shadow: var(--lg-glass-shadow);
  
  /* Tag 组件 */
  --el-tag-border-radius: var(--lg-radius-full);
  --el-tag-border-color: var(--lg-glass-border);
}

/* 液态玻璃容器类 */
.liquid-glass-container {
  /* 为容器内的 Element Plus 组件应用液态玻璃效果 */
  
  /* Button 特殊效果 */
  :deep(.el-button) {
    backdrop-filter: blur(20px) saturate(180%);
    transition: all 0.3s var(--lg-ease-smooth);
    border-radius: var(--lg-radius-2xl);
  }
  
  :deep(.el-button:hover) {
    transform: translateY(-2px);
    box-shadow: var(--lg-glass-shadow-hover);
  }
  
  /* Input 特殊效果 */
  :deep(.el-input__wrapper) {
    backdrop-filter: blur(20px) saturate(180%);
    box-shadow: var(--lg-glass-shadow);
  }
  
  :deep(.el-input__wrapper:hover) {
    box-shadow: var(--lg-glass-shadow-hover);
  }
  
  :deep(.el-input__wrapper.is-focus) {
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1), var(--lg-glass-shadow);
  }
  
  /* Dialog 特殊效果 */
  :deep(.el-dialog) {
    backdrop-filter: blur(20px) saturate(180%);
    border: 1px solid var(--lg-glass-border);
  }
  
  :deep(.el-dialog__header) {
    border-bottom: 1px solid var(--lg-glass-border);
    padding: 1.5rem;
  }
  
  :deep(.el-dialog__title) {
    color: var(--lg-text-primary);
    font-weight: 600;
    font-size: 1.25rem;
  }
  
  :deep(.el-dialog__body) {
    padding: 0 1.5rem;
    color: var(--lg-text-primary);
  }
  
  :deep(.el-dialog__footer) {
    border-top: 1px solid var(--lg-glass-border);
    padding: 1.5rem;
  }
  
  /* Tag 特殊效果 */
  :deep(.el-tag) {
    backdrop-filter: blur(20px) saturate(180%);
    border: 1px solid var(--lg-glass-border);
  }
  
  /* Form 组件文字颜色 */
  :deep(.el-form-item__label) {
    color: var(--lg-text-primary);
    font-weight: 500;
  }
  
  :deep(.el-checkbox__label) {
    color: var(--lg-text-primary);
  }
  
  :deep(.el-radio__label) {
    color: var(--lg-text-primary);
  }
  
  :deep(.el-switch__label) {
    color: var(--lg-text-primary);
  }
  
  /* Empty 组件 */
  :deep(.el-empty__description) {
    color: rgba(255, 255, 255, 0.8);
  }
  
  /* Icon 组件 */
  :deep(.el-icon) {
    font-size: 1.2em;
  }
}
